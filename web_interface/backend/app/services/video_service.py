# -*- coding: utf-8 -*-
"""
视频生成相关业务逻辑服务
"""

import os
import uuid
import asyncio
from typing import Optional, List, Dict, Any
from datetime import datetime
from pathlib import Path

from app.models.video import (
    VideoGenerationRequest,
    VideoGenerationResponse,
    VideoTaskInfo,
    VideoStatus,
    VideoGenerationProgress,
    ParallelVideoRequest,
    ParallelVideoResponse,
    ChapterStatus
)
from app.config import Settings, get_chapter_path, get_novel_step3_path
from app.utils.logger import get_logger
from app.utils.time_utils import get_current_timestamp

logger = get_logger(__name__)


class VideoService:
    """视频服务类"""

    def __init__(self, settings: Settings):
        self.settings = settings
        # 内存中的任务存储（生产环境应该使用数据库）
        self.tasks: Dict[str, VideoTaskInfo] = {}
        # 并行任务存储
        self.parallel_tasks: Dict[str, ParallelVideoResponse] = {}
    
    async def create_generation_task(self, request: VideoGenerationRequest) -> str:
        """创建视频生成任务"""
        try:
            task_id = str(uuid.uuid4())
            current_time = get_current_timestamp()
            
            # 创建任务信息
            task_info = VideoTaskInfo(
                task_id=task_id,
                novel_name=request.novel_name,
                chapter_name=request.chapter_name,
                status=VideoStatus.PENDING,
                created_at=current_time,
                updated_at=current_time,
                skip_violation_check=request.skip_violation_check,
                skip_mosaic=request.skip_mosaic
            )
            
            # 存储任务
            self.tasks[task_id] = task_info
            
            logger.info(f"创建视频生成任务: {task_id}")
            return task_id
            
        except Exception as e:
            logger.error(f"创建视频生成任务失败: {e}")
            raise
    
    async def process_video_generation(self, task_id: str, request: VideoGenerationRequest):
        """处理视频生成（后台任务）"""
        try:
            # 更新任务状态为处理中
            await self._update_task_status(task_id, VideoStatus.PROCESSING)
            
            # 模拟视频生成过程
            await self._simulate_video_generation(task_id, request)
            
        except Exception as e:
            logger.error(f"视频生成失败 {task_id}: {e}")
            await self._update_task_status(
                task_id, 
                VideoStatus.FAILED, 
                error_message=str(e)
            )
    
    async def get_task_status(self, task_id: str) -> Optional[VideoGenerationResponse]:
        """获取任务状态"""
        try:
            task_info = self.tasks.get(task_id)
            if not task_info:
                return None
            
            # 构建响应
            response = VideoGenerationResponse(
                task_id=task_id,
                status=task_info.status,
                created_at=task_info.created_at,
                started_at=getattr(task_info, 'started_at', None),
                completed_at=getattr(task_info, 'completed_at', None),
                output_url=task_info.output_url,
                local_file_path=task_info.local_file_path,
                error_message=task_info.error_message
            )
            
            return response
            
        except Exception as e:
            logger.error(f"获取任务状态失败 {task_id}: {e}")
            raise
    
    async def get_all_tasks(
        self, 
        novel_name: Optional[str] = None,
        status: Optional[VideoStatus] = None,
        limit: int = 50
    ) -> List[VideoTaskInfo]:
        """获取所有任务"""
        try:
            tasks = list(self.tasks.values())
            
            # 筛选
            if novel_name:
                tasks = [t for t in tasks if t.novel_name == novel_name]
            
            if status:
                tasks = [t for t in tasks if t.status == status]
            
            # 按创建时间倒序排列
            tasks.sort(key=lambda x: x.created_at, reverse=True)
            
            # 限制数量
            return tasks[:limit]
            
        except Exception as e:
            logger.error(f"获取任务列表失败: {e}")
            raise
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        try:
            task_info = self.tasks.get(task_id)
            if not task_info:
                return False
            
            if task_info.status not in [VideoStatus.PENDING, VideoStatus.PROCESSING]:
                return False
            
            await self._update_task_status(task_id, VideoStatus.CANCELLED)
            
            logger.info(f"任务已取消: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"取消任务失败 {task_id}: {e}")
            raise
    
    async def get_task_info(self, task_id: str) -> Optional[VideoTaskInfo]:
        """获取任务信息"""
        return self.tasks.get(task_id)
    
    async def get_video_download_info(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取视频下载信息"""
        try:
            task_info = self.tasks.get(task_id)
            if not task_info or task_info.status != VideoStatus.COMPLETED:
                return None
            
            return {
                "task_id": task_id,
                "download_url": task_info.output_url,
                "file_path": task_info.local_file_path,
                "file_size": getattr(task_info, 'file_size', None),
                "duration": getattr(task_info, 'duration', None)
            }
            
        except Exception as e:
            logger.error(f"获取下载信息失败 {task_id}: {e}")
            raise
    
    async def _update_task_status(
        self, 
        task_id: str, 
        status: VideoStatus, 
        error_message: Optional[str] = None,
        **kwargs
    ):
        """更新任务状态"""
        task_info = self.tasks.get(task_id)
        if not task_info:
            return
        
        task_info.status = status
        task_info.updated_at = get_current_timestamp()
        
        if error_message:
            task_info.error_message = error_message
        
        # 更新其他字段
        for key, value in kwargs.items():
            if hasattr(task_info, key):
                setattr(task_info, key, value)
        
        # 设置时间戳
        if status == VideoStatus.PROCESSING and not hasattr(task_info, 'started_at'):
            task_info.started_at = get_current_timestamp()
        elif status in [VideoStatus.COMPLETED, VideoStatus.FAILED, VideoStatus.CANCELLED]:
            task_info.completed_at = get_current_timestamp()
    
    async def _simulate_video_generation(self, task_id: str, request: VideoGenerationRequest):
        """模拟视频生成过程"""
        try:
            # 模拟不同阶段的处理
            stages = [
                ("读取分镜数据", 10),
                ("处理图片", 30),
                ("生成音频", 20),
                ("合成视频", 30),
                ("后处理", 10)
            ]
            
            total_progress = 0
            
            for stage_name, stage_duration in stages:
                logger.info(f"任务 {task_id} 开始阶段: {stage_name}")
                
                # 模拟处理时间
                await asyncio.sleep(stage_duration / 10)  # 缩短模拟时间
                
                total_progress += stage_duration
                
                # 更新进度（这里应该通过WebSocket或其他方式实时推送）
                progress = VideoGenerationProgress(
                    current_step=stage_name,
                    progress_percentage=min(total_progress, 100),
                    estimated_time_remaining=max(0, (100 - total_progress) * 0.5)
                )
                
                # 在实际实现中，这里应该更新任务的进度信息
                logger.info(f"任务 {task_id} 进度: {total_progress}%")
            
            # 生成输出文件路径
            output_dir = get_novel_step3_path(request.novel_name)
            os.makedirs(output_dir, exist_ok=True)
            
            output_filename = f"{request.chapter_name}.mp4"
            output_path = os.path.join(output_dir, output_filename)
            
            # 模拟生成视频文件
            with open(output_path, 'w') as f:
                f.write("模拟视频文件内容")
            
            # 生成输出URL（实际应该是TOS或其他存储的URL）
            output_url = f"https://example-bucket.tos.volcengine.com/videos/{request.novel_name}/{output_filename}"
            
            # 更新任务状态为完成
            await self._update_task_status(
                task_id,
                VideoStatus.COMPLETED,
                output_url=output_url,
                local_file_path=output_path,
                file_size=1024 * 1024,  # 模拟文件大小
                duration=120.0  # 模拟视频时长
            )
            
            logger.info(f"视频生成完成: {task_id}")
            
        except Exception as e:
            logger.error(f"视频生成过程失败 {task_id}: {e}")
            raise

    async def create_parallel_task(self, request: ParallelVideoRequest) -> str:
        """创建并行处理任务"""
        try:
            task_id = str(uuid.uuid4())
            current_time = get_current_timestamp()

            # 创建并行任务信息
            task_info = ParallelVideoResponse(
                task_id=task_id,
                status=VideoStatus.PENDING,
                message="并行处理任务已创建",
                created_at=current_time
            )

            # 存储任务
            self.parallel_tasks[task_id] = task_info

            logger.info(f"创建并行处理任务: {task_id}, 小说: {request.novel_name}")
            return task_id

        except Exception as e:
            logger.error(f"创建并行处理任务失败: {e}")
            raise

    async def process_parallel_video_generation(self, task_id: str, request: ParallelVideoRequest):
        """处理并行视频生成（后台任务）"""
        try:
            # 更新任务状态为处理中
            await self._update_parallel_task_status(task_id, VideoStatus.PROCESSING, "开始并行处理")

            # 导入并行处理器
            import sys
            from pathlib import Path
            project_root = Path(__file__).parent.parent.parent.parent
            sys.path.insert(0, str(project_root))

            from src.video_synthesis.parallel_video_processor import ParallelVideoProcessor

            # 构建小说目录路径
            novel_dir = f"output/novel_step2/{request.novel_name}"

            # 创建并行处理器
            processor = ParallelVideoProcessor(
                novel_dir=novel_dir,
                output_base_dir=request.output_dir,
                max_workers=request.max_workers
            )

            # 查找章节
            chapters = processor.find_chapters()
            if not chapters:
                raise Exception("未找到任何章节")

            # 选择要处理的章节
            if request.auto_all or request.chapter_range == "all":
                selected_chapters = chapters
            elif request.chapter_range:
                selected_chapters = processor.parse_chapter_range(chapters, request.chapter_range)
            else:
                # 获取待处理章节
                pending_chapters = []
                for chapter in chapters:
                    if not processor._is_chapter_video_completed(chapter['chapter_name']):
                        pending_chapters.append(chapter)
                selected_chapters = pending_chapters

            if not selected_chapters:
                await self._update_parallel_task_status(
                    task_id,
                    VideoStatus.COMPLETED,
                    "所有章节都已完成，无需处理",
                    total_chapters=0,
                    successful_chapters=0,
                    failed_chapters=0
                )
                return

            # 更新任务信息
            await self._update_parallel_task_status(
                task_id,
                VideoStatus.PROCESSING,
                f"开始处理 {len(selected_chapters)} 个章节",
                total_chapters=len(selected_chapters)
            )

            # 处理章节
            result = processor.process_selected_chapters(selected_chapters)

            # 构建章节结果
            chapter_results = []
            for chapter_name, chapter_result in result.get('results', {}).items():
                status = VideoStatus.COMPLETED if chapter_result.get('success') else VideoStatus.FAILED
                chapter_status = ChapterStatus(
                    chapter_name=chapter_name,
                    status=status,
                    output_url=chapter_result.get('output_url'),
                    local_file_path=chapter_result.get('local_file_path'),
                    error_message=chapter_result.get('error') if not chapter_result.get('success') else None
                )
                chapter_results.append(chapter_status)

            # 更新最终状态
            successful_count = result.get('successful_chapters', 0)
            total_count = result.get('total_chapters', 0)
            failed_count = total_count - successful_count

            final_status = VideoStatus.COMPLETED if successful_count > 0 else VideoStatus.FAILED
            message = f"处理完成：成功 {successful_count}/{total_count} 个章节"

            await self._update_parallel_task_status(
                task_id,
                final_status,
                message,
                total_chapters=total_count,
                successful_chapters=successful_count,
                failed_chapters=failed_count,
                chapter_results=chapter_results
            )

            logger.info(f"并行处理完成: {task_id}, 成功: {successful_count}/{total_count}")

        except Exception as e:
            logger.error(f"并行处理失败 {task_id}: {e}")
            await self._update_parallel_task_status(
                task_id,
                VideoStatus.FAILED,
                f"处理失败: {str(e)}",
                error_message=str(e)
            )

    async def get_parallel_task_status(self, task_id: str) -> Optional[ParallelVideoResponse]:
        """获取并行任务状态"""
        try:
            return self.parallel_tasks.get(task_id)
        except Exception as e:
            logger.error(f"获取并行任务状态失败 {task_id}: {e}")
            raise

    async def _update_parallel_task_status(
        self,
        task_id: str,
        status: VideoStatus,
        message: str,
        **kwargs
    ):
        """更新并行任务状态"""
        task_info = self.parallel_tasks.get(task_id)
        if not task_info:
            return

        task_info.status = status
        task_info.message = message

        # 更新其他字段
        for key, value in kwargs.items():
            if hasattr(task_info, key):
                setattr(task_info, key, value)

        # 设置时间戳
        current_time = get_current_timestamp()
        if status == VideoStatus.PROCESSING and not task_info.started_at:
            task_info.started_at = current_time
        elif status in [VideoStatus.COMPLETED, VideoStatus.FAILED, VideoStatus.CANCELLED]:
            task_info.completed_at = current_time
