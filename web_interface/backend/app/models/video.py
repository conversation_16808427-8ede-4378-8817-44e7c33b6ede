# -*- coding: utf-8 -*-
"""
视频生成相关数据模型
"""

from typing import Optional, List
from enum import Enum
from pydantic import BaseModel, Field


class VideoStatus(str, Enum):
    """视频生成状态枚举"""
    PENDING = "pending"          # 等待中
    PROCESSING = "processing"    # 处理中
    COMPLETED = "completed"      # 已完成
    FAILED = "failed"           # 失败
    CANCELLED = "cancelled"      # 已取消


class VideoGenerationRequest(BaseModel):
    """视频生成请求模型"""
    novel_name: str = Field(description="小说名称")
    chapter_name: str = Field(description="章节名称")
    skip_violation_check: bool = Field(default=True, description="跳过违规检测")
    skip_mosaic: bool = Field(default=True, description="跳过打码处理")
    output_dir: Optional[str] = Field(default=None, description="输出目录")
    max_workers: int = Field(default=1, description="最大并发数")


class VideoGenerationProgress(BaseModel):
    """视频生成进度模型"""
    current_step: str = Field(description="当前步骤")
    progress_percentage: float = Field(description="进度百分比 0-100")
    estimated_time_remaining: Optional[int] = Field(default=None, description="预计剩余时间(秒)")
    current_segment: Optional[int] = Field(default=None, description="当前处理的分镜")
    total_segments: Optional[int] = Field(default=None, description="总分镜数")


class VideoGenerationResponse(BaseModel):
    """视频生成响应模型"""
    task_id: str = Field(description="任务ID")
    status: VideoStatus = Field(description="生成状态")
    progress: Optional[VideoGenerationProgress] = Field(default=None, description="进度信息")
    
    # 成功时的结果
    output_url: Optional[str] = Field(default=None, description="生成的视频URL")
    local_file_path: Optional[str] = Field(default=None, description="本地文件路径")
    file_size: Optional[int] = Field(default=None, description="文件大小")
    duration: Optional[float] = Field(default=None, description="视频时长(秒)")
    
    # 失败时的错误信息
    error_message: Optional[str] = Field(default=None, description="错误信息")
    error_details: Optional[List[str]] = Field(default=None, description="详细错误信息")
    
    # 时间信息
    created_at: Optional[str] = Field(default=None, description="创建时间")
    started_at: Optional[str] = Field(default=None, description="开始时间")
    completed_at: Optional[str] = Field(default=None, description="完成时间")


class VideoTaskInfo(BaseModel):
    """视频任务信息模型"""
    task_id: str = Field(description="任务ID")
    novel_name: str = Field(description="小说名称")
    chapter_name: str = Field(description="章节名称")
    status: VideoStatus = Field(description="任务状态")
    created_at: str = Field(description="创建时间")
    updated_at: str = Field(description="更新时间")

    # 配置信息
    skip_violation_check: bool = Field(description="是否跳过违规检测")
    skip_mosaic: bool = Field(description="是否跳过打码")

    # 结果信息
    output_url: Optional[str] = Field(default=None, description="输出URL")
    local_file_path: Optional[str] = Field(default=None, description="本地文件路径")
    error_message: Optional[str] = Field(default=None, description="错误信息")


class ParallelVideoRequest(BaseModel):
    """并行视频处理请求"""
    novel_name: str = Field(description="小说名称")
    output_dir: str = Field(default="output/novel_step3", description="输出目录")
    max_workers: int = Field(default=3, ge=1, le=100, description="最大并行工作线程数")
    chapter_range: Optional[str] = Field(default=None, description="章节范围，如 '1-5,8,10-15' 或 'all'")
    auto_all: bool = Field(default=False, description="自动处理所有章节")


class ChapterStatus(BaseModel):
    """章节状态信息"""
    chapter_name: str = Field(description="章节名称")
    status: VideoStatus = Field(description="处理状态")
    output_url: Optional[str] = Field(default=None, description="输出视频URL")
    local_file_path: Optional[str] = Field(default=None, description="本地文件路径")
    error_message: Optional[str] = Field(default=None, description="错误信息")


class ParallelVideoResponse(BaseModel):
    """并行视频处理响应"""
    task_id: str = Field(description="任务ID")
    status: VideoStatus = Field(description="任务状态")
    message: str = Field(description="状态消息")
    created_at: Optional[str] = Field(default=None, description="创建时间")
    started_at: Optional[str] = Field(default=None, description="开始时间")
    completed_at: Optional[str] = Field(default=None, description="完成时间")
    total_chapters: Optional[int] = Field(default=None, description="总章节数")
    successful_chapters: Optional[int] = Field(default=None, description="成功章节数")
    failed_chapters: Optional[int] = Field(default=None, description="失败章节数")
    chapter_results: Optional[List[ChapterStatus]] = Field(default=None, description="章节处理结果")
    error_message: Optional[str] = Field(default=None, description="错误信息")
