# -*- coding: utf-8 -*-
"""
视频生成相关API路由
"""

from typing import List
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from app.models.video import (
    VideoGenerationRequest,
    VideoGenerationResponse,
    VideoTaskInfo,
    VideoStatus,
    ParallelVideoRequest,
    ParallelVideoResponse,
    SimpleVideoRequest,
    SimpleVideoResponse
)
from app.services.video_service import VideoService
from app.config import get_settings, Settings

router = APIRouter()


def get_video_service(settings: Settings = Depends(get_settings)) -> VideoService:
    """获取视频服务实例"""
    return VideoService(settings)


@router.post("/generate", response_model=VideoGenerationResponse)
async def generate_video(
    request: VideoGenerationRequest,
    background_tasks: BackgroundTasks,
    video_service: VideoService = Depends(get_video_service)
):
    """生成视频（异步任务）"""
    try:
        # 创建视频生成任务
        task_id = await video_service.create_generation_task(request)
        
        # 添加后台任务
        background_tasks.add_task(
            video_service.process_video_generation,
            task_id,
            request
        )
        
        return VideoGenerationResponse(
            task_id=task_id,
            status=VideoStatus.PENDING,
            progress=None
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建视频生成任务失败: {str(e)}")


@router.get("/tasks/{task_id}", response_model=VideoGenerationResponse)
async def get_task_status(
    task_id: str,
    video_service: VideoService = Depends(get_video_service)
):
    """获取视频生成任务状态"""
    try:
        task_info = await video_service.get_task_status(task_id)
        if not task_info:
            raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")
        
        return task_info
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")


@router.get("/tasks", response_model=List[VideoTaskInfo])
async def get_all_tasks(
    novel_name: str = None,
    status: VideoStatus = None,
    limit: int = 50,
    video_service: VideoService = Depends(get_video_service)
):
    """获取所有视频生成任务"""
    try:
        tasks = await video_service.get_all_tasks(
            novel_name=novel_name,
            status=status,
            limit=limit
        )
        return tasks
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")


@router.delete("/tasks/{task_id}")
async def cancel_task(
    task_id: str,
    video_service: VideoService = Depends(get_video_service)
):
    """取消视频生成任务"""
    try:
        success = await video_service.cancel_task(task_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在或无法取消")
        
        return {
            "success": True,
            "message": "任务取消成功",
            "task_id": task_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")


@router.post("/tasks/{task_id}/retry", response_model=VideoGenerationResponse)
async def retry_task(
    task_id: str,
    background_tasks: BackgroundTasks,
    video_service: VideoService = Depends(get_video_service)
):
    """重试失败的视频生成任务"""
    try:
        # 获取原始任务信息
        original_task = await video_service.get_task_info(task_id)
        if not original_task:
            raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")
        
        if original_task.status not in [VideoStatus.FAILED, VideoStatus.CANCELLED]:
            raise HTTPException(
                status_code=400, 
                detail=f"只能重试失败或已取消的任务，当前状态: {original_task.status}"
            )
        
        # 创建新的重试任务
        retry_request = VideoGenerationRequest(
            novel_name=original_task.novel_name,
            chapter_name=original_task.chapter_name,
            skip_violation_check=original_task.skip_violation_check,
            skip_mosaic=original_task.skip_mosaic
        )
        
        new_task_id = await video_service.create_generation_task(retry_request)
        
        # 添加后台任务
        background_tasks.add_task(
            video_service.process_video_generation,
            new_task_id,
            retry_request
        )
        
        return VideoGenerationResponse(
            task_id=new_task_id,
            status=VideoStatus.PENDING,
            progress=None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重试任务失败: {str(e)}")


@router.get("/download/{task_id}")
async def download_video(
    task_id: str,
    video_service: VideoService = Depends(get_video_service)
):
    """下载生成的视频文件"""
    try:
        file_info = await video_service.get_video_download_info(task_id)
        if not file_info:
            raise HTTPException(status_code=404, detail="视频文件不存在")

        return file_info

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取下载信息失败: {str(e)}")


@router.post("/parallel-process", response_model=ParallelVideoResponse)
async def parallel_video_process(
    request: ParallelVideoRequest,
    background_tasks: BackgroundTasks,
    video_service: VideoService = Depends(get_video_service)
):
    """并行处理小说章节视频剪辑"""
    try:
        # 创建并行处理任务
        task_id = await video_service.create_parallel_task(request)

        # 添加后台任务
        background_tasks.add_task(
            video_service.process_parallel_video_generation,
            task_id,
            request
        )

        return ParallelVideoResponse(
            task_id=task_id,
            status=VideoStatus.PENDING,
            message="并行视频处理任务已创建"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建并行处理任务失败: {str(e)}")


@router.get("/parallel-tasks/{task_id}", response_model=ParallelVideoResponse)
async def get_parallel_task_status(
    task_id: str,
    video_service: VideoService = Depends(get_video_service)
):
    """获取并行处理任务状态"""
    try:
        task_info = await video_service.get_parallel_task_status(task_id)
        if not task_info:
            raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")

        return task_info

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取并行任务状态失败: {str(e)}")


@router.post("/simple-generate", response_model=SimpleVideoResponse)
async def simple_video_generate(
    request: SimpleVideoRequest,
    background_tasks: BackgroundTasks,
    video_service: VideoService = Depends(get_video_service)
):
    """简单视频生成（单章节JSON）"""
    try:
        # 创建简单视频生成任务
        task_id = await video_service.create_simple_task(request)

        # 添加后台任务
        background_tasks.add_task(
            video_service.process_simple_video_generation,
            task_id,
            request
        )

        return SimpleVideoResponse(
            task_id=task_id,
            status=VideoStatus.PENDING,
            message="视频生成任务已创建"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建视频生成任务失败: {str(e)}")


@router.get("/simple-tasks/{task_id}", response_model=SimpleVideoResponse)
async def get_simple_task_status(
    task_id: str,
    video_service: VideoService = Depends(get_video_service)
):
    """获取简单任务状态"""
    try:
        task_info = await video_service.get_simple_task_status(task_id)
        if not task_info:
            # 如果任务不存在，返回一个默认的失败状态
            # 这通常意味着任务已经处理完成但被清理了
            return SimpleVideoResponse(
                task_id=task_id,
                status=VideoStatus.FAILED,
                message="任务已完成或已过期",
                error_message="任务数据已被清理，可能是由于服务重启或任务过期"
            )

        return task_info

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")
