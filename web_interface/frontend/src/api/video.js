import api from './index'

export const videoApi = {
  // 生成视频
  generateVideo(requestData) {
    return api.post('/videos/generate', requestData)
  },
  
  // 获取任务状态
  getTaskStatus(taskId) {
    return api.get(`/videos/tasks/${taskId}`)
  },
  
  // 获取所有任务
  getAllTasks(filters = {}) {
    const params = new URLSearchParams()
    
    if (filters.novel_name) {
      params.append('novel_name', filters.novel_name)
    }
    if (filters.status) {
      params.append('status', filters.status)
    }
    if (filters.limit) {
      params.append('limit', filters.limit)
    }
    
    const queryString = params.toString()
    return api.get(`/videos/tasks${queryString ? '?' + queryString : ''}`)
  },
  
  // 取消任务
  cancelTask(taskId) {
    return api.delete(`/videos/tasks/${taskId}`)
  },
  
  // 重试任务
  retryTask(taskId) {
    return api.post(`/videos/tasks/${taskId}/retry`)
  },
  
  // 获取视频下载信息
  getVideoDownloadInfo(taskId) {
    return api.get(`/videos/download/${taskId}`)
  },

  // 并行处理视频
  parallelProcess(requestData) {
    return api.post('/videos/parallel-process', requestData)
  },

  // 获取并行任务状态
  getParallelTaskStatus(taskId) {
    return api.get(`/videos/parallel-tasks/${taskId}`)
  },

  // 简单视频生成
  simpleGenerate(requestData) {
    return api.post('/videos/simple-generate', requestData)
  },

  // 获取简单任务状态
  getSimpleTaskStatus(taskId) {
    return api.get(`/videos/simple-tasks/${taskId}`)
  }
}
