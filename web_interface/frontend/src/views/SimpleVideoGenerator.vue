<template>
  <div class="simple-video-generator">
    <div class="header">
      <h1>🎬 简易视频生成器</h1>
      <p>粘贴章节JSON数据，一键生成视频</p>
    </div>

    <div class="main-content">
      <!-- JSON输入区域 -->
      <el-card class="json-input-card" v-if="!isProcessing && !result">
        <template #header>
          <span>📝 输入章节JSON数据</span>
        </template>
        
        <el-form :model="form" ref="formRef">
          <el-form-item label="章节名称" prop="chapterName">
            <el-input 
              v-model="form.chapterName" 
              placeholder="例如: chapter1_风起大秦公子华"
              style="width: 300px"
            />
          </el-form-item>
          
          <el-form-item label="JSON数据" prop="jsonData">
            <el-input
              v-model="form.jsonData"
              type="textarea"
              :rows="15"
              placeholder="请粘贴章节的storyboards.json内容..."
              style="font-family: monospace;"
            />
          </el-form-item>
          
          <el-form-item label="并行线程数">
            <el-input-number 
              v-model="form.maxWorkers" 
              :min="1" 
              :max="100" 
              style="width: 150px"
            />
            <span style="margin-left: 10px; color: #666;">建议设置为CPU核心数的2-4倍</span>
          </el-form-item>
          
          <el-form-item>
            <el-button 
              type="primary" 
              size="large"
              @click="generateVideo"
              :loading="loading"
            >
              <el-icon><VideoPlay /></el-icon>
              生成视频
            </el-button>
            
            <el-button @click="clearForm" style="margin-left: 10px;">
              <el-icon><Delete /></el-icon>
              清空
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 处理状态 -->
      <el-card v-if="isProcessing" class="processing-card">
        <template #header>
          <span>⚡ 正在处理中...</span>
        </template>
        
        <div class="processing-content">
          <el-progress 
            :percentage="progress" 
            :status="progressStatus"
            :stroke-width="8"
          />
          
          <div class="status-info">
            <p><strong>任务ID:</strong> {{ taskId }}</p>
            <p><strong>章节:</strong> {{ form.chapterName }}</p>
            <p><strong>状态:</strong> {{ statusMessage }}</p>
            <p><strong>开始时间:</strong> {{ startTime }}</p>
          </div>
          
          <div class="processing-logs" v-if="logs.length > 0">
            <h4>处理日志:</h4>
            <div class="log-container">
              <div 
                v-for="(log, index) in logs" 
                :key="index" 
                class="log-item"
                :class="log.type"
              >
                {{ log.message }}
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 处理结果 -->
      <el-card v-if="result" class="result-card">
        <template #header>
          <div class="result-header">
            <span v-if="result.success">✅ 处理完成</span>
            <span v-else>❌ 处理失败</span>
            
            <el-button 
              type="primary" 
              size="small"
              @click="startNew"
            >
              生成新视频
            </el-button>
          </div>
        </template>
        
        <div class="result-content">
          <div v-if="result.success" class="success-result">
            <div class="result-info">
              <p><strong>章节:</strong> {{ form.chapterName }}</p>
              <p><strong>视频URL:</strong> 
                <a :href="result.outputUrl" target="_blank" v-if="result.outputUrl">
                  {{ result.outputUrl }}
                </a>
                <span v-else>暂无</span>
              </p>
              <p><strong>本地路径:</strong> {{ result.localPath || '暂无' }}</p>
              <p><strong>处理时间:</strong> {{ result.duration }}</p>
            </div>
            
            <div class="result-actions">
              <el-button 
                type="success" 
                @click="downloadVideo"
                v-if="result.outputUrl"
              >
                <el-icon><Download /></el-icon>
                下载视频
              </el-button>
              
              <el-button 
                @click="copyUrl"
                v-if="result.outputUrl"
              >
                <el-icon><CopyDocument /></el-icon>
                复制链接
              </el-button>
            </div>
          </div>
          
          <div v-else class="error-result">
            <el-alert
              title="处理失败"
              :description="result.error"
              type="error"
              show-icon
              :closable="false"
            />
            
            <div class="error-details" v-if="result.details">
              <h4>错误详情:</h4>
              <pre>{{ result.details }}</pre>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { VideoPlay, Delete, Download, CopyDocument } from '@element-plus/icons-vue'
import { videoApi } from '@/api/video'

// 响应式数据
const loading = ref(false)
const isProcessing = ref(false)
const result = ref(null)
const taskId = ref('')
const progress = ref(0)
const progressStatus = ref('')
const statusMessage = ref('')
const startTime = ref('')
const logs = ref([])
const formRef = ref()

// 表单数据
const form = reactive({
  chapterName: '',
  jsonData: '',
  maxWorkers: 60
})

// 轮询定时器
let pollTimer = null

// 清理定时器
onUnmounted(() => {
  if (pollTimer) {
    clearInterval(pollTimer)
  }
})

// 生成视频
const generateVideo = async () => {
  if (!form.chapterName.trim()) {
    ElMessage.error('请输入章节名称')
    return
  }
  
  if (!form.jsonData.trim()) {
    ElMessage.error('请输入JSON数据')
    return
  }
  
  // 验证JSON格式
  try {
    JSON.parse(form.jsonData)
  } catch (error) {
    ElMessage.error('JSON格式错误，请检查输入')
    return
  }
  
  loading.value = true
  
  try {
    // 调用真实的API
    const response = await videoApi.simpleGenerate({
      chapter_name: form.chapterName,
      json_data: form.jsonData,
      max_workers: form.maxWorkers
    })

    // 开始处理状态
    taskId.value = response.data.task_id
    isProcessing.value = true
    startTime.value = new Date().toLocaleString()
    statusMessage.value = response.data.message || '任务已创建，开始处理...'

    // 开始轮询任务状态
    startPolling()

    ElMessage.success('视频生成任务已启动')

  } catch (error) {
    ElMessage.error('启动失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    loading.value = false
  }
}

// 开始轮询任务状态
const startPolling = () => {
  if (pollTimer) {
    clearInterval(pollTimer)
  }

  pollTimer = setInterval(async () => {
    if (!taskId.value) return

    try {
      const response = await videoApi.getSimpleTaskStatus(taskId.value)
      const taskInfo = response.data

      statusMessage.value = taskInfo.message || '处理中...'

      // 根据状态更新进度
      if (taskInfo.status === 'processing') {
        // 模拟进度增长
        if (progress.value < 90) {
          progress.value += Math.random() * 10
        }
      } else if (taskInfo.status === 'completed') {
        progress.value = 100
        progressStatus.value = 'success'
        completeProcessing(true, taskInfo)
        clearInterval(pollTimer)
        pollTimer = null
      } else if (taskInfo.status === 'failed') {
        progressStatus.value = 'exception'
        completeProcessing(false, taskInfo.error_message || '处理失败')
        clearInterval(pollTimer)
        pollTimer = null
      }

    } catch (error) {
      console.error('轮询状态失败:', error)
    }
  }, 2000) // 每2秒轮询一次
}



// 添加日志
const addLog = (message, type = 'info') => {
  logs.value.push({
    message: `[${new Date().toLocaleTimeString()}] ${message}`,
    type: type
  })
  
  // 限制日志数量
  if (logs.value.length > 20) {
    logs.value.shift()
  }
}

// 完成处理
const completeProcessing = (success, taskInfoOrError = null) => {
  isProcessing.value = false

  if (success && taskInfoOrError) {
    result.value = {
      success: true,
      outputUrl: taskInfoOrError.output_url,
      localPath: taskInfoOrError.local_file_path,
      duration: calculateDuration()
    }
    progressStatus.value = 'success'
    addLog('视频生成完成！', 'success')
    ElMessage.success('视频生成完成！')
  } else {
    const error = typeof taskInfoOrError === 'string' ? taskInfoOrError : (taskInfoOrError?.error_message || '处理过程中发生未知错误')
    result.value = {
      success: false,
      error: error,
      details: error
    }
    progressStatus.value = 'exception'
    addLog('处理失败: ' + error, 'error')
    ElMessage.error('视频生成失败: ' + error)
  }
}

// 计算处理时长
const calculateDuration = () => {
  const start = new Date(startTime.value)
  const end = new Date()
  const duration = Math.round((end - start) / 1000)
  return `${Math.floor(duration / 60)}分${duration % 60}秒`
}

// 下载视频
const downloadVideo = () => {
  if (result.value?.outputUrl) {
    window.open(result.value.outputUrl, '_blank')
  }
}

// 复制链接
const copyUrl = async () => {
  if (result.value?.outputUrl) {
    try {
      await navigator.clipboard.writeText(result.value.outputUrl)
      ElMessage.success('链接已复制到剪贴板')
    } catch (error) {
      ElMessage.error('复制失败')
    }
  }
}

// 开始新任务
const startNew = () => {
  result.value = null
  isProcessing.value = false
  progress.value = 0
  logs.value = []
  taskId.value = ''
  statusMessage.value = ''
  startTime.value = ''
}

// 清空表单
const clearForm = () => {
  form.chapterName = ''
  form.jsonData = ''
  form.maxWorkers = 60
}
</script>

<style scoped>
.simple-video-generator {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  color: #409EFF;
  margin-bottom: 10px;
}

.header p {
  color: #666;
  font-size: 16px;
}

.json-input-card, .processing-card, .result-card {
  margin-bottom: 20px;
}

.processing-content {
  text-align: center;
}

.status-info {
  margin: 20px 0;
  text-align: left;
}

.status-info p {
  margin: 8px 0;
}

.processing-logs {
  margin-top: 20px;
  text-align: left;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
}

.log-item {
  margin: 2px 0;
}

.log-item.info {
  color: #409EFF;
}

.log-item.success {
  color: #67C23A;
}

.log-item.error {
  color: #F56C6C;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-info p {
  margin: 10px 0;
}

.result-actions {
  margin-top: 20px;
}

.error-details {
  margin-top: 15px;
}

.error-details pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}
</style>
