<template>
  <div class="parallel-video-processor">
    <div class="header">
      <h1>📹 并行视频剪辑处理</h1>
      <p class="subtitle">批量处理小说章节视频剪辑，支持多线程并行处理</p>
    </div>

    <!-- 配置表单 -->
    <el-card class="config-card" v-if="!currentTask">
      <template #header>
        <div class="card-header">
          <span>⚙️ 处理配置</span>
        </div>
      </template>

      <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
        <el-form-item label="小说选择" prop="novel_name">
          <el-select 
            v-model="form.novel_name" 
            placeholder="请选择要处理的小说"
            style="width: 100%"
            @change="onNovelChange"
          >
            <el-option
              v-for="novel in novels"
              :key="novel"
              :label="novel"
              :value="novel"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="输出目录" prop="output_dir">
          <el-input v-model="form.output_dir" placeholder="输出目录路径" />
        </el-form-item>

        <el-form-item label="并行线程数" prop="max_workers">
          <el-input-number 
            v-model="form.max_workers" 
            :min="1" 
            :max="100" 
            style="width: 200px"
          />
          <span class="help-text">建议设置为CPU核心数的2-4倍</span>
        </el-form-item>

        <el-form-item label="章节范围" prop="chapter_range">
          <el-radio-group v-model="rangeType" @change="onRangeTypeChange">
            <el-radio label="all">处理所有待处理章节</el-radio>
            <el-radio label="custom">自定义范围</el-radio>
          </el-radio-group>
          
          <el-input 
            v-if="rangeType === 'custom'"
            v-model="form.chapter_range"
            placeholder="例如: 1-5,8,10-15"
            style="margin-top: 10px"
          />
          <div v-if="rangeType === 'custom'" class="help-text">
            格式说明：单个章节(5)、连续章节(1-10)、多个范围(1-5,8,10-15)
          </div>
        </el-form-item>

        <el-form-item>
          <el-button 
            type="primary" 
            @click="startProcessing"
            :loading="loading"
            size="large"
          >
            <el-icon><VideoPlay /></el-icon>
            开始处理
          </el-button>
          
          <el-button @click="resetForm">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 处理状态 -->
    <el-card v-if="currentTask" class="status-card">
      <template #header>
        <div class="card-header">
          <span>📊 处理状态</span>
          <el-button 
            type="danger" 
            size="small" 
            @click="cancelTask"
            v-if="currentTask.status === 'processing'"
          >
            取消任务
          </el-button>
        </div>
      </template>

      <div class="task-info">
        <div class="task-meta">
          <p><strong>任务ID:</strong> {{ currentTask.task_id }}</p>
          <p><strong>小说:</strong> {{ form.novel_name }}</p>
          <p><strong>状态:</strong> 
            <el-tag :type="getStatusType(currentTask.status)">
              {{ getStatusText(currentTask.status) }}
            </el-tag>
          </p>
          <p><strong>消息:</strong> {{ currentTask.message }}</p>
        </div>

        <div v-if="currentTask.total_chapters" class="progress-info">
          <p><strong>总章节数:</strong> {{ currentTask.total_chapters }}</p>
          <p v-if="currentTask.successful_chapters !== null">
            <strong>成功:</strong> {{ currentTask.successful_chapters }}
          </p>
          <p v-if="currentTask.failed_chapters !== null">
            <strong>失败:</strong> {{ currentTask.failed_chapters }}
          </p>
        </div>

        <div class="time-info">
          <p v-if="currentTask.created_at">
            <strong>创建时间:</strong> {{ formatTime(currentTask.created_at) }}
          </p>
          <p v-if="currentTask.started_at">
            <strong>开始时间:</strong> {{ formatTime(currentTask.started_at) }}
          </p>
          <p v-if="currentTask.completed_at">
            <strong>完成时间:</strong> {{ formatTime(currentTask.completed_at) }}
          </p>
        </div>
      </div>

      <!-- 章节处理结果 -->
      <div v-if="currentTask.chapter_results && currentTask.chapter_results.length > 0" class="chapter-results">
        <h3>📋 章节处理结果</h3>
        <el-table :data="currentTask.chapter_results" style="width: 100%">
          <el-table-column prop="chapter_name" label="章节名称" />
          <el-table-column prop="status" label="状态">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="output_url" label="输出URL" show-overflow-tooltip />
          <el-table-column prop="error_message" label="错误信息" show-overflow-tooltip />
        </el-table>
      </div>

      <div class="actions" style="margin-top: 20px;">
        <el-button @click="resetTask">
          <el-icon><Back /></el-icon>
          返回配置
        </el-button>
        
        <el-button 
          v-if="currentTask.status === 'completed' || currentTask.status === 'failed'"
          type="primary"
          @click="startProcessing"
        >
          <el-icon><Refresh /></el-icon>
          重新处理
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { VideoPlay, Refresh, Back } from '@element-plus/icons-vue'
import { videoApi } from '@/api/video'
import { novelApi } from '@/api/novel'

// 响应式数据
const loading = ref(false)
const novels = ref([])
const currentTask = ref(null)
const rangeType = ref('all')
const formRef = ref()

// 表单数据
const form = reactive({
  novel_name: '',
  output_dir: 'output/novel_step3',
  max_workers: 60,
  chapter_range: '',
  auto_all: true
})

// 表单验证规则
const rules = {
  novel_name: [
    { required: true, message: '请选择小说', trigger: 'change' }
  ],
  output_dir: [
    { required: true, message: '请输入输出目录', trigger: 'blur' }
  ],
  max_workers: [
    { required: true, message: '请设置并行线程数', trigger: 'blur' }
  ]
}

// 轮询定时器
let pollTimer = null

// 生命周期
onMounted(() => {
  loadNovels()
})

onUnmounted(() => {
  if (pollTimer) {
    clearInterval(pollTimer)
  }
})

// 方法
const loadNovels = async () => {
  try {
    const response = await novelApi.getNovels()
    novels.value = response.data.novels || []
  } catch (error) {
    ElMessage.error('加载小说列表失败: ' + error.message)
  }
}

const onNovelChange = () => {
  // 小说改变时可以做一些处理
}

const onRangeTypeChange = () => {
  if (rangeType.value === 'all') {
    form.auto_all = true
    form.chapter_range = ''
  } else {
    form.auto_all = false
  }
}

const startProcessing = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    loading.value = true
    
    const requestData = {
      novel_name: form.novel_name,
      output_dir: form.output_dir,
      max_workers: form.max_workers,
      auto_all: form.auto_all,
      chapter_range: form.chapter_range || null
    }
    
    const response = await videoApi.parallelProcess(requestData)
    currentTask.value = response.data
    
    ElMessage.success('任务创建成功，开始处理...')
    
    // 开始轮询状态
    startPolling()
    
  } catch (error) {
    ElMessage.error('启动处理失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const startPolling = () => {
  if (pollTimer) {
    clearInterval(pollTimer)
  }
  
  pollTimer = setInterval(async () => {
    if (!currentTask.value) return
    
    try {
      const response = await videoApi.getParallelTaskStatus(currentTask.value.task_id)
      currentTask.value = response.data
      
      // 如果任务完成，停止轮询
      if (['completed', 'failed', 'cancelled'].includes(currentTask.value.status)) {
        clearInterval(pollTimer)
        pollTimer = null
        
        if (currentTask.value.status === 'completed') {
          ElMessage.success('处理完成！')
        } else if (currentTask.value.status === 'failed') {
          ElMessage.error('处理失败: ' + (currentTask.value.error_message || '未知错误'))
        }
      }
    } catch (error) {
      console.error('轮询状态失败:', error)
    }
  }, 2000) // 每2秒轮询一次
}

const cancelTask = async () => {
  try {
    await ElMessageBox.confirm('确定要取消当前任务吗？', '确认取消', {
      type: 'warning'
    })
    
    // 这里应该调用取消API，但目前并行任务没有取消接口
    ElMessage.info('取消功能暂未实现')
    
  } catch (error) {
    // 用户取消
  }
}

const resetTask = () => {
  currentTask.value = null
  if (pollTimer) {
    clearInterval(pollTimer)
    pollTimer = null
  }
}

const resetForm = () => {
  form.novel_name = ''
  form.output_dir = 'output/novel_step3'
  form.max_workers = 60
  form.chapter_range = ''
  form.auto_all = true
  rangeType.value = 'all'
}

const getStatusType = (status) => {
  const statusMap = {
    pending: 'info',
    processing: 'warning',
    completed: 'success',
    failed: 'danger',
    cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    pending: '等待中',
    processing: '处理中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const formatTime = (timeStr) => {
  if (!timeStr) return ''
  return new Date(timeStr).toLocaleString()
}
</script>

<style scoped>
.parallel-video-processor {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  color: #409EFF;
  margin-bottom: 10px;
}

.subtitle {
  color: #666;
  font-size: 16px;
}

.config-card, .status-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.help-text {
  color: #999;
  font-size: 12px;
  margin-left: 10px;
}

.task-info {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.task-meta p, .progress-info p, .time-info p {
  margin: 5px 0;
}

.chapter-results {
  margin-top: 20px;
}

.chapter-results h3 {
  margin-bottom: 15px;
  color: #409EFF;
}

.actions {
  text-align: center;
}

@media (max-width: 768px) {
  .task-info {
    grid-template-columns: 1fr;
  }
}
</style>
