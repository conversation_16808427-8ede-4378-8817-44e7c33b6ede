<template>
  <div class="home-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">小说视频生成系统</h1>
      <p class="page-description">
        欢迎使用小说视频生成Web界面，您可以在这里管理小说章节、替换图片并生成视频
      </p>
    </div>
    
    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon size="32" color="#409eff">
            <Document />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.totalNovels }}</div>
          <div class="stat-label">小说总数</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon size="32" color="#67c23a">
            <Files />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.totalChapters }}</div>
          <div class="stat-label">章节总数</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon size="32" color="#e6a23c">
            <VideoCamera />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.generatedVideos }}</div>
          <div class="stat-label">已生成视频</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon size="32" color="#f56c6c">
            <Clock />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.pendingTasks }}</div>
          <div class="stat-label">待处理任务</div>
        </div>
      </div>
    </div>
    
    <!-- 快速操作 -->
    <div class="quick-actions">
      <h2 class="section-title">快速操作</h2>
      <div class="action-grid">
        <el-card class="action-card" @click="goToNovels">
          <div class="action-icon">
            <el-icon size="48">
              <Document />
            </el-icon>
          </div>
          <h3 class="action-title">管理小说</h3>
          <p class="action-description">查看和管理所有小说章节</p>
        </el-card>
        
        <el-card class="action-card" @click="goToVideos">
          <div class="action-icon">
            <el-icon size="48">
              <VideoCamera />
            </el-icon>
          </div>
          <h3 class="action-title">视频任务</h3>
          <p class="action-description">查看视频生成任务状态</p>
        </el-card>
        
        <el-card class="action-card" @click="goToSimpleGenerator">
          <div class="action-icon">
            <el-icon size="48">
              <VideoPlay />
            </el-icon>
          </div>
          <h3 class="action-title">简易视频生成</h3>
          <p class="action-description">粘贴JSON数据，一键生成视频</p>
        </el-card>

        <el-card class="action-card" @click="goToParallelProcessor">
          <div class="action-icon">
            <el-icon size="48">
              <VideoCamera />
            </el-icon>
          </div>
          <h3 class="action-title">并行视频处理</h3>
          <p class="action-description">批量处理小说章节视频剪辑</p>
        </el-card>

        <el-card class="action-card" @click="goToSettings">
          <div class="action-icon">
            <el-icon size="48">
              <Setting />
            </el-icon>
          </div>
          <h3 class="action-title">系统设置</h3>
          <p class="action-description">配置系统参数和选项</p>
        </el-card>
      </div>
    </div>
    
    <!-- 最近活动 -->
    <div class="recent-activity">
      <h2 class="section-title">最近活动</h2>
      <el-card>
        <el-timeline>
          <el-timeline-item
            v-for="activity in recentActivities"
            :key="activity.id"
            :timestamp="activity.timestamp"
            :type="activity.type"
          >
            {{ activity.description }}
          </el-timeline-item>
        </el-timeline>
        
        <div v-if="recentActivities.length === 0" class="empty-state">
          <el-icon size="48" color="#c0c4cc">
            <DocumentRemove />
          </el-icon>
          <p>暂无最近活动</p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Document,
  Files,
  VideoCamera,
  Clock,
  Setting,
  DocumentRemove,
  VideoPlay
} from '@element-plus/icons-vue'
import { useNovelStore } from '@/stores/novel'
import { useVideoStore } from '@/stores/video'
import { useGlobalStore } from '@/stores/global'

const router = useRouter()
const novelStore = useNovelStore()
const videoStore = useVideoStore()
const globalStore = useGlobalStore()

// 统计数据
const stats = ref({
  totalNovels: 0,
  totalChapters: 0,
  generatedVideos: 0,
  pendingTasks: 0
})

// 最近活动
const recentActivities = ref([])

// 页面挂载时加载数据
onMounted(async () => {
  await loadStats()
  await loadRecentActivities()
})

// 加载统计数据
const loadStats = async () => {
  try {
    globalStore.setLoading(true, '加载统计数据...')
    
    // 加载小说数据
    const novels = await novelStore.fetchNovels()
    stats.value.totalNovels = novels.length
    stats.value.totalChapters = novels.reduce((sum, novel) => sum + novel.total_chapters, 0)
    stats.value.generatedVideos = novels.reduce((sum, novel) => sum + novel.generated_videos, 0)
    
    // 加载视频任务数据
    const tasks = await videoStore.fetchAllTasks()
    stats.value.pendingTasks = tasks.filter(task => 
      ['pending', 'processing'].includes(task.status)
    ).length
    
  } catch (error) {
    console.error('加载统计数据失败:', error)
  } finally {
    globalStore.setLoading(false)
  }
}

// 加载最近活动
const loadRecentActivities = async () => {
  try {
    // 这里可以从API获取最近活动数据
    // 暂时使用模拟数据
    recentActivities.value = [
      {
        id: 1,
        description: '完成了《王府继兄宠我如宝》第130章的图片替换',
        timestamp: '2024-01-15 14:30:00',
        type: 'success'
      },
      {
        id: 2,
        description: '开始生成《大唐太子》第1章视频',
        timestamp: '2024-01-15 14:25:00',
        type: 'primary'
      },
      {
        id: 3,
        description: '上传了5张替换图片',
        timestamp: '2024-01-15 14:20:00',
        type: 'info'
      }
    ]
  } catch (error) {
    console.error('加载最近活动失败:', error)
  }
}

// 导航到不同页面
const goToNovels = () => {
  router.push('/novels')
}

const goToVideos = () => {
  router.push('/videos')
}

const goToSimpleGenerator = () => {
  router.push('/simple-generator')
}

const goToParallelProcessor = () => {
  router.push('/parallel-processor')
}

const goToSettings = () => {
  router.push('/settings')
}
</script>

<style lang="scss" scoped>
.home-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: $spacing-lg;
}

.page-header {
  text-align: center;
  margin-bottom: $spacing-xxl;
  
  .page-title {
    font-size: 2.5rem;
    font-weight: $font-weight-bold;
    color: var(--el-text-color-primary);
    margin-bottom: $spacing-md;
  }
  
  .page-description {
    font-size: $font-size-lg;
    color: var(--el-text-color-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: $spacing-lg;
  margin-bottom: $spacing-xxl;
}

.stat-card {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: $border-radius-large;
  padding: $spacing-lg;
  display: flex;
  align-items: center;
  gap: $spacing-md;
  transition: $transition-base;
  
  &:hover {
    box-shadow: $box-shadow-light;
    transform: translateY(-2px);
  }
  
  .stat-icon {
    flex-shrink: 0;
  }
  
  .stat-content {
    .stat-number {
      font-size: 2rem;
      font-weight: $font-weight-bold;
      color: var(--el-text-color-primary);
      line-height: 1;
    }
    
    .stat-label {
      font-size: $font-size-sm;
      color: var(--el-text-color-secondary);
      margin-top: 4px;
    }
  }
}

.section-title {
  font-size: $font-size-xl;
  font-weight: $font-weight-medium;
  color: var(--el-text-color-primary);
  margin-bottom: $spacing-lg;
}

.quick-actions {
  margin-bottom: $spacing-xxl;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: $spacing-lg;
}

.action-card {
  text-align: center;
  cursor: pointer;
  transition: $transition-base;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: $box-shadow-dark;
  }
  
  .action-icon {
    color: var(--el-color-primary);
    margin-bottom: $spacing-md;
  }
  
  .action-title {
    font-size: $font-size-lg;
    font-weight: $font-weight-medium;
    color: var(--el-text-color-primary);
    margin-bottom: $spacing-sm;
  }
  
  .action-description {
    color: var(--el-text-color-secondary);
    font-size: $font-size-sm;
    line-height: 1.4;
  }
}

.recent-activity {
  .empty-state {
    text-align: center;
    padding: $spacing-xxl;
    color: var(--el-text-color-secondary);
    
    p {
      margin-top: $spacing-md;
      font-size: $font-size-base;
    }
  }
}

// 响应式设计
@media (max-width: $breakpoint-md) {
  .home-page {
    padding: $spacing-md;
  }
  
  .page-header .page-title {
    font-size: 2rem;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: $spacing-md;
  }
  
  .action-grid {
    grid-template-columns: 1fr;
    gap: $spacing-md;
  }
}

@media (max-width: $breakpoint-sm) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    padding: $spacing-md;
  }
}
</style>
