import { createRouter, createWebHistory } from 'vue-router'
import { useGlobalStore } from '@/stores/global'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ 
  showSpinner: false,
  minimum: 0.2,
  speed: 500
})

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: {
      title: '首页',
      keepAlive: true
    }
  },
  {
    path: '/novels',
    name: 'NovelList',
    component: () => import('@/views/NovelList.vue'),
    meta: {
      title: '小说列表',
      keepAlive: true
    }
  },
  {
    path: '/novels/:novelName',
    name: 'NovelDetail',
    component: () => import('@/views/NovelDetail.vue'),
    meta: {
      title: '小说详情',
      keepAlive: false
    }
  },
  {
    path: '/novels/:novelName/chapters/:chapterName',
    name: 'ChapterEditor',
    component: () => import('@/views/ChapterEditor.vue'),
    meta: {
      title: '章节编辑',
      keepAlive: false
    }
  },
  {
    path: '/videos',
    name: 'VideoTasks',
    component: () => import('@/views/VideoTasks.vue'),
    meta: {
      title: '视频任务',
      keepAlive: true
    }
  },
  {
    path: '/videos/:taskId',
    name: 'VideoTaskDetail',
    component: () => import('@/views/VideoTaskDetail.vue'),
    meta: {
      title: '任务详情',
      keepAlive: false
    }
  },
  {
    path: '/parallel-processor',
    name: 'ParallelVideoProcessor',
    component: () => import('@/views/ParallelVideoProcessor.vue'),
    meta: {
      title: '并行视频处理',
      keepAlive: false
    }
  },
  {
    path: '/simple-generator',
    name: 'SimpleVideoGenerator',
    component: () => import('@/views/SimpleVideoGenerator.vue'),
    meta: {
      title: '简易视频生成器',
      keepAlive: false
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('@/views/Settings.vue'),
    meta: {
      title: '系统设置',
      keepAlive: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面未找到'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  NProgress.start()
  
  // 设置页面标题
  const title = to.meta.title
  if (title) {
    document.title = `${title} - 小说视频生成`
  }
  
  next()
})

// 全局后置钩子
router.afterEach((to, from) => {
  NProgress.done()
  
  // 清除全局加载状态
  const globalStore = useGlobalStore()
  globalStore.setLoading(false)
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
  NProgress.done()
})

export default router
