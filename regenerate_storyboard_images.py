#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新生成storyboards.json文件中的图片URL
"""

import os
import json
import asyncio
import logging
import base64
from pathlib import Path
from typing import Dict, Any, Optional
from dotenv import load_dotenv
from volcengine.visual.VisualService import VisualService

# 加载环境变量
load_dotenv()

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ImageRegenerator:
    """图片重新生成器"""
    
    def __init__(self):
        """初始化生成器"""
        # 获取API密钥
        self.access_key = os.environ.get("VOLCENGINE_ACCESS_KEY")
        self.secret_key = os.environ.get("VOLCENGINE_SECRET_KEY")
        self.model = os.environ.get("VOLCENGINE_IMAGE_GEN_MODEL", "high_aes_general_v20_L")
        
        if not self.access_key:
            raise ValueError("请设置环境变量 VOLCENGINE_ACCESS_KEY")
        if not self.secret_key:
            raise ValueError("请设置环境变量 VOLCENGINE_SECRET_KEY")
            
        # 解码base64编码的密钥（如果需要）
        try:
            self.access_key = base64.b64decode(self.access_key).decode('utf-8')
            self.secret_key = base64.b64decode(self.secret_key).decode('utf-8')
            logger.info("使用解码后的API密钥")
        except:
            logger.info("使用原始API密钥")
            
        logger.info(f"使用模型: {self.model}")
        
    def get_image_url_from_response(self, response: Dict[str, Any]) -> Optional[str]:
        """从API响应中提取图像URL"""
        try:
            if response.get("code") == 10000:
                data = response.get("data", {})
                
                # 尝试获取URL
                if "image_url" in data:
                    return data["image_url"]
                elif "binary_data_base64" in data:
                    return "base64_data_available"
                else:
                    logger.warning("响应中未找到图像URL或base64数据")
                    return None
            else:
                error_msg = response.get("message", "未知错误")
                logger.error(f"API返回错误: {error_msg}")
                return None
        except Exception as e:
            logger.error(f"解析响应时发生错误: {str(e)}")
            return None
    
    def generate_image_sync(self, prompt: str, negative_prompt: str = "", 
                           width: int = 720, height: int = 1280, 
                           seed: int = -1, scale: float = 7.5, 
                           ddim_steps: int = 16) -> Dict[str, Any]:
        """同步生成图像"""
        try:
            # 创建服务实例
            visual_service = VisualService()
            visual_service.set_ak(self.access_key)
            visual_service.set_sk(self.secret_key)
            
            # 构建请求参数
            form = {
                "req_key": self.model,
                "prompt": prompt,
                "negative_prompt": negative_prompt,
                "seed": seed,
                "scale": scale,
                "ddim_steps": ddim_steps,
                "width": width,
                "height": height,
                "use_sr": True,
                "use_pre_llm": True,
                "return_url": True,
                "logo_info": {"add_logo": False},
            }
            
            logger.info(f"正在生成图像: {prompt[:50]}...")
            logger.info(f"参数: {width}x{height}, seed={seed}, scale={scale}")
            
            # 调用API
            response = visual_service.cv_process(form)
            
            if response.get("code") == 10000:
                logger.info("图像生成成功")
                return response
            else:
                error_msg = response.get("message", "未知错误")
                logger.error(f"图像生成失败: {error_msg}")
                raise Exception(f"API调用失败: {error_msg}")
                
        except Exception as e:
            logger.error(f"生成图像时发生错误: {str(e)}")
            raise
    
    async def generate_image_async(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """异步生成图像"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.generate_image_sync, prompt, **kwargs)
    
    async def regenerate_storyboard_images(self, storyboard_file: str, output_file: str = None):
        """重新生成storyboard文件中的所有图片"""
        if output_file is None:
            output_file = storyboard_file.replace('.json', '_new.json')
        
        # 读取原始文件
        with open(storyboard_file, 'r', encoding='utf-8') as f:
            storyboards = json.load(f)
        
        logger.info(f"开始处理 {len(storyboards)} 个故事板")
        
        # 处理每个故事板
        for i, storyboard in enumerate(storyboards):
            logger.info(f"处理第 {i+1}/{len(storyboards)} 个故事板")
            
            # 获取图片提示词
            image_prompt = storyboard.get('image_prompt', '')
            if not image_prompt:
                logger.warning(f"第 {i+1} 个故事板没有image_prompt，跳过")
                continue
            
            try:
                # 生成新图片
                response = await self.generate_image_async(image_prompt)
                new_image_url = self.get_image_url_from_response(response)
                
                if new_image_url and new_image_url != "base64_data_available":
                    # 更新图片URL
                    old_url = storyboard.get('image_url', '')
                    storyboard['image_url'] = new_image_url
                    logger.info(f"✅ 第 {i+1} 个故事板图片更新成功")
                    logger.info(f"   旧URL: {old_url[:50]}...")
                    logger.info(f"   新URL: {new_image_url[:50]}...")
                else:
                    logger.error(f"❌ 第 {i+1} 个故事板图片生成失败")
                
                # 添加延迟避免API限制
                await asyncio.sleep(2)
                
            except Exception as e:
                logger.error(f"❌ 第 {i+1} 个故事板处理失败: {str(e)}")
                continue
        
        # 保存新文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(storyboards, f, ensure_ascii=False, indent=2)
        
        logger.info(f"✅ 处理完成，新文件保存为: {output_file}")


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='重新生成storyboard图片')
    parser.add_argument('input_file', help='输入的storyboards.json文件路径')
    parser.add_argument('-o', '--output', help='输出文件路径（可选）')
    
    args = parser.parse_args()
    
    try:
        # 创建生成器
        regenerator = ImageRegenerator()
        
        # 重新生成图片
        await regenerator.regenerate_storyboard_images(args.input_file, args.output)
        
    except Exception as e:
        logger.error(f"❌ 错误: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
