#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
启动Web界面的便捷脚本
"""

import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)
    print(f"✅ Python版本: {sys.version}")

def check_node_version():
    """检查Node.js版本"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js版本: {result.stdout.strip()}")
            return True
        else:
            print("❌ Node.js未安装")
            return False
    except FileNotFoundError:
        print("❌ Node.js未安装")
        return False

def install_backend_dependencies():
    """安装后端依赖"""
    print("\n📦 安装后端依赖...")
    backend_dir = Path("web_interface/backend")
    
    if not backend_dir.exists():
        print("❌ 后端目录不存在")
        return False
    
    # 安装Python依赖
    requirements_file = backend_dir / "requirements.txt"
    if requirements_file.exists():
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
            ], check=True, cwd=backend_dir)
            print("✅ 后端依赖安装完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 后端依赖安装失败: {e}")
            return False
    else:
        print("❌ requirements.txt文件不存在")
        return False

def install_frontend_dependencies():
    """安装前端依赖"""
    print("\n📦 安装前端依赖...")
    frontend_dir = Path("web_interface/frontend")
    
    if not frontend_dir.exists():
        print("❌ 前端目录不存在")
        return False
    
    # 检查package.json
    package_json = frontend_dir / "package.json"
    if not package_json.exists():
        print("❌ package.json文件不存在")
        return False
    
    # 安装npm依赖
    try:
        subprocess.run(["npm", "install"], check=True, cwd=frontend_dir)
        print("✅ 前端依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 前端依赖安装失败: {e}")
        return False

def start_backend():
    """启动后端服务"""
    print("\n🚀 启动后端服务...")
    backend_dir = Path("web_interface/backend")
    
    try:
        # 启动FastAPI服务
        process = subprocess.Popen([
            sys.executable, "run.py"
        ], cwd=backend_dir)
        
        print("✅ 后端服务启动中...")
        print("📍 后端地址: http://localhost:8000")
        print("📖 API文档: http://localhost:8000/docs")
        
        return process
    except Exception as e:
        print(f"❌ 后端服务启动失败: {e}")
        return None

def start_frontend():
    """启动前端服务"""
    print("\n🚀 启动前端服务...")
    frontend_dir = Path("web_interface/frontend")
    
    try:
        # 启动Vue开发服务器
        process = subprocess.Popen([
            "npm", "run", "dev"
        ], cwd=frontend_dir)
        
        print("✅ 前端服务启动中...")
        print("📍 前端地址: http://localhost:3000")
        
        return process
    except Exception as e:
        print(f"❌ 前端服务启动失败: {e}")
        return None

def wait_for_services():
    """等待服务启动"""
    print("\n⏳ 等待服务启动...")
    time.sleep(5)
    
    # 尝试打开浏览器
    try:
        webbrowser.open("http://localhost:3000")
        print("🌐 已在浏览器中打开Web界面")
    except Exception as e:
        print(f"⚠️ 无法自动打开浏览器: {e}")
        print("请手动访问: http://localhost:3000")

def main():
    """主函数"""
    print("🎬 小说视频生成Web界面启动器")
    print("=" * 50)
    
    # 检查环境
    check_python_version()
    
    if not check_node_version():
        print("\n请先安装Node.js: https://nodejs.org/")
        sys.exit(1)
    
    # 安装依赖
    if not install_backend_dependencies():
        sys.exit(1)
    
    if not install_frontend_dependencies():
        sys.exit(1)
    
    # 启动服务
    backend_process = start_backend()
    if not backend_process:
        sys.exit(1)
    
    time.sleep(3)  # 等待后端启动
    
    frontend_process = start_frontend()
    if not frontend_process:
        backend_process.terminate()
        sys.exit(1)
    
    # 等待服务启动并打开浏览器
    wait_for_services()
    
    print("\n" + "=" * 50)
    print("🎉 Web界面启动成功！")
    print("📍 前端地址: http://localhost:3000")
    print("📍 后端地址: http://localhost:8000")
    print("📖 API文档: http://localhost:8000/docs")
    print("\n按 Ctrl+C 停止服务")
    print("=" * 50)
    
    try:
        # 等待用户中断
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n\n🛑 正在停止服务...")
        
        # 停止进程
        if frontend_process:
            frontend_process.terminate()
            print("✅ 前端服务已停止")
        
        if backend_process:
            backend_process.terminate()
            print("✅ 后端服务已停止")
        
        print("👋 再见！")

if __name__ == "__main__":
    main()
