# 📹 小说视频剪辑Web界面

## 概述

这是一个基于Vue.js + FastAPI的Web界面，专门为你的小说视频剪辑项目设计。通过这个界面，你可以方便地运行原本需要命令行操作的视频剪辑功能。

## 🎯 主要功能

### 1. 并行视频处理
- **替代命令行**: 不再需要手动输入复杂的命令行参数
- **可视化配置**: 通过表单界面设置所有参数
- **实时监控**: 查看处理进度和状态
- **批量处理**: 支持选择章节范围或处理所有章节

### 2. 原命令行功能对比

**原命令行**:
```bash
python src/video_synthesis/parallel_video_processor.py \
  --novel_dir output/novel_step2/大唐太子：开局硬刚李世民 \
  --output_dir output/novel_step3 \
  --max_workers 60
```

**现在的Web界面**:
- 🎯 下拉选择小说名称
- 📁 输入框设置输出目录
- ⚙️ 滑块调整并行线程数
- 📋 选择章节范围（全部/自定义）
- 🚀 一键启动处理

## 🚀 快速开始

### 方法一：使用启动脚本（推荐）

1. **运行启动脚本**:
   ```bash
   python3 start_web_interface.py
   ```

2. **自动完成**:
   - ✅ 检查Python和Node.js环境
   - 📦 自动安装依赖
   - 🚀 启动后端和前端服务
   - 🌐 自动打开浏览器

3. **访问界面**:
   - 前端地址: http://localhost:3000
   - 后端API: http://localhost:8000
   - API文档: http://localhost:8000/docs

### 方法二：手动启动

1. **启动后端**:
   ```bash
   cd web_interface/backend
   pip install -r requirements.txt
   python run.py
   ```

2. **启动前端**:
   ```bash
   cd web_interface/frontend
   npm install
   npm run dev
   ```

## 📋 使用步骤

### 1. 访问并行处理页面
- 打开Web界面后，点击首页的"并行视频处理"卡片
- 或直接访问: http://localhost:3000/parallel-processor

### 2. 配置处理参数
- **小说选择**: 从下拉列表选择要处理的小说
- **输出目录**: 设置视频输出路径（默认: output/novel_step3）
- **并行线程数**: 设置同时处理的线程数（建议: CPU核心数的2-4倍）
- **章节范围**: 
  - 选择"处理所有待处理章节"：自动处理未完成的章节
  - 选择"自定义范围"：手动指定章节，如 `1-5,8,10-15`

### 3. 开始处理
- 点击"开始处理"按钮
- 系统会创建后台任务并开始处理
- 页面会自动切换到状态监控界面

### 4. 监控进度
- **实时状态**: 显示当前处理状态（等待中/处理中/已完成/失败）
- **进度信息**: 显示总章节数、成功数、失败数
- **章节详情**: 查看每个章节的处理结果
- **时间信息**: 显示创建、开始、完成时间

## 🔧 技术架构

### 后端 (FastAPI)
- **框架**: FastAPI + Python 3.8+
- **核心功能**: 
  - 集成现有的 `ParallelVideoProcessor`
  - 提供RESTful API接口
  - 支持异步任务处理
  - 实时状态更新

### 前端 (Vue.js)
- **框架**: Vue.js 3 + Element Plus
- **核心功能**:
  - 响应式用户界面
  - 实时状态轮询
  - 表单验证和提交
  - 进度可视化

## 📁 项目结构

```
web_interface/
├── backend/                 # 后端API服务
│   ├── app/
│   │   ├── routers/        # API路由
│   │   ├── services/       # 业务逻辑
│   │   ├── models/         # 数据模型
│   │   └── main.py         # 应用入口
│   └── requirements.txt    # Python依赖
│
├── frontend/               # 前端Vue应用
│   ├── src/
│   │   ├── views/          # 页面组件
│   │   ├── api/            # API调用
│   │   └── components/     # 通用组件
│   └── package.json        # Node.js依赖
│
start_web_interface.py      # 一键启动脚本
```

## 🎯 核心优势

### 1. 用户友好
- ✅ 无需记忆复杂命令行参数
- ✅ 可视化配置界面
- ✅ 实时进度反馈
- ✅ 错误信息清晰显示

### 2. 功能完整
- ✅ 完全兼容原有命令行功能
- ✅ 支持所有原有参数配置
- ✅ 保持相同的处理逻辑
- ✅ 输出结果完全一致

### 3. 易于扩展
- ✅ 模块化设计
- ✅ RESTful API架构
- ✅ 可轻松添加新功能
- ✅ 支持多用户使用

## 🔍 API接口

### 并行处理接口
```http
POST /api/videos/parallel-process
Content-Type: application/json

{
  "novel_name": "大唐太子：开局硬刚李世民",
  "output_dir": "output/novel_step3",
  "max_workers": 60,
  "auto_all": true,
  "chapter_range": null
}
```

### 状态查询接口
```http
GET /api/videos/parallel-tasks/{task_id}
```

## 🛠️ 环境要求

- **Python**: 3.8+
- **Node.js**: 14+
- **操作系统**: Windows/macOS/Linux
- **浏览器**: Chrome/Firefox/Safari/Edge

## 📝 注意事项

1. **首次运行**: 需要安装依赖，可能需要几分钟时间
2. **端口占用**: 确保8000和3000端口未被占用
3. **数据路径**: 确保 `output/novel_step2/` 目录存在且包含小说数据
4. **资源消耗**: 并行处理会消耗大量CPU和内存资源

## 🎉 总结

通过这个Web界面，你可以：
- 🚀 **一键启动**: 运行 `python3 start_web_interface.py`
- 🎯 **简单配置**: 通过表单设置所有参数
- 📊 **实时监控**: 查看处理进度和结果
- 💻 **跨平台**: 支持所有主流操作系统

不再需要记忆复杂的命令行参数，享受可视化的视频剪辑处理体验！
