# 📹 小说视频剪辑Web界面

## 概述

这是一个基于Vue.js + FastAPI的Web界面，专门为你的小说视频剪辑项目设计。提供两种使用方式：**简易视频生成器**（推荐）和并行视频处理器。

## 🎯 主要功能

### 1. 简易视频生成器 ⭐ **推荐使用**
- **极简操作**: 只需粘贴章节JSON数据，一键生成视频
- **直接输入**: 复制粘贴storyboards.json内容即可
- **实时监控**: 查看处理进度和状态
- **即时反馈**: 处理完成后立即显示结果

### 2. 原命令行功能对比

**原命令行**:
```bash
python src/video_synthesis/parallel_video_processor.py \
  --novel_dir output/novel_step2/大唐太子：开局硬刚李世民 \
  --output_dir output/novel_step3 \
  --max_workers 60
```

**现在的简易Web界面**:
- 📝 直接粘贴章节JSON数据
- 🎬 输入章节名称
- ⚙️ 设置并行线程数
- 🚀 一键生成视频

## 🚀 快速开始

### 方法一：使用启动脚本（推荐）

1. **运行启动脚本**:
   ```bash
   python3 start_web_interface.py
   ```

2. **自动完成**:
   - ✅ 检查Python和Node.js环境
   - 📦 自动安装依赖
   - 🚀 启动后端和前端服务
   - 🌐 自动打开浏览器

3. **访问界面**:
   - 前端地址: http://localhost:3000
   - 后端API: http://localhost:8000
   - API文档: http://localhost:8000/docs

### 方法二：手动启动

1. **启动后端**:
   ```bash
   cd web_interface/backend
   pip install -r requirements.txt
   python run.py
   ```

2. **启动前端**:
   ```bash
   cd web_interface/frontend
   npm install
   npm run dev
   ```

## 📋 使用步骤

### 方式一：简易视频生成器 ⭐ **推荐**

1. **访问简易生成器页面**
   - 打开Web界面后，点击首页的"简易视频生成"卡片
   - 或直接访问: http://localhost:3000/simple-generator

2. **准备章节数据**
   - 找到你要处理的章节目录，如：`output/novel_step2/大唐太子：开局硬刚李世民/chapter1_风起大秦公子华/`
   - 打开其中的 `storyboards.json` 文件
   - 复制整个JSON内容

3. **填写生成参数**
   - **章节名称**: 输入章节名称，如 `chapter1_风起大秦公子华`
   - **JSON数据**: 粘贴刚才复制的storyboards.json内容
   - **并行线程数**: 设置线程数（默认60，建议CPU核心数的2-4倍）

4. **开始生成**
   - 点击"生成视频"按钮
   - 系统会验证JSON格式并开始处理
   - 实时查看处理进度和日志

5. **获取结果**
   - 处理完成后会显示视频URL和本地路径
   - 可以直接下载或复制链接

### 方式二：并行视频处理器（批量处理）

1. **访问并行处理页面**
   - 点击首页的"并行视频处理"卡片
   - 或直接访问: http://localhost:3000/parallel-processor

2. **配置处理参数**
   - **小说选择**: 从下拉列表选择要处理的小说
   - **输出目录**: 设置视频输出路径（默认: output/novel_step3）
   - **并行线程数**: 设置同时处理的线程数
   - **章节范围**: 选择要处理的章节范围

3. **批量处理**
   - 系统会自动处理多个章节
   - 显示整体进度和每个章节的状态

## 🔧 技术架构

### 后端 (FastAPI)
- **框架**: FastAPI + Python 3.8+
- **核心功能**: 
  - 集成现有的 `ParallelVideoProcessor`
  - 提供RESTful API接口
  - 支持异步任务处理
  - 实时状态更新

### 前端 (Vue.js)
- **框架**: Vue.js 3 + Element Plus
- **核心功能**:
  - 响应式用户界面
  - 实时状态轮询
  - 表单验证和提交
  - 进度可视化

## 📁 项目结构

```
web_interface/
├── backend/                 # 后端API服务
│   ├── app/
│   │   ├── routers/        # API路由
│   │   ├── services/       # 业务逻辑
│   │   ├── models/         # 数据模型
│   │   └── main.py         # 应用入口
│   └── requirements.txt    # Python依赖
│
├── frontend/               # 前端Vue应用
│   ├── src/
│   │   ├── views/          # 页面组件
│   │   ├── api/            # API调用
│   │   └── components/     # 通用组件
│   └── package.json        # Node.js依赖
│
start_web_interface.py      # 一键启动脚本
```

## 🎯 核心优势

### 1. 用户友好
- ✅ 无需记忆复杂命令行参数
- ✅ 可视化配置界面
- ✅ 实时进度反馈
- ✅ 错误信息清晰显示

### 2. 功能完整
- ✅ 完全兼容原有命令行功能
- ✅ 支持所有原有参数配置
- ✅ 保持相同的处理逻辑
- ✅ 输出结果完全一致

### 3. 易于扩展
- ✅ 模块化设计
- ✅ RESTful API架构
- ✅ 可轻松添加新功能
- ✅ 支持多用户使用

## 🔍 API接口

### 简易视频生成接口
```http
POST /api/videos/simple-generate
Content-Type: application/json

{
  "chapter_name": "chapter1_风起大秦公子华",
  "json_data": "[{\"chapter\": 1, \"story_board\": \"...\", ...}]",
  "max_workers": 60
}
```

### 简易任务状态查询
```http
GET /api/videos/simple-tasks/{task_id}
```

### 并行处理接口
```http
POST /api/videos/parallel-process
Content-Type: application/json

{
  "novel_name": "大唐太子：开局硬刚李世民",
  "output_dir": "output/novel_step3",
  "max_workers": 60,
  "auto_all": true,
  "chapter_range": null
}
```

## 🛠️ 环境要求

- **Python**: 3.8+
- **Node.js**: 14+
- **操作系统**: Windows/macOS/Linux
- **浏览器**: Chrome/Firefox/Safari/Edge

## 📝 注意事项

1. **首次运行**: 需要安装依赖，可能需要几分钟时间
2. **端口占用**: 确保8000和3000端口未被占用
3. **数据路径**: 确保 `output/novel_step2/` 目录存在且包含小说数据
4. **资源消耗**: 并行处理会消耗大量CPU和内存资源

## 🎉 总结

通过这个Web界面，你可以：
- 🚀 **一键启动**: 运行 `python3 start_web_interface.py`
- 📝 **极简操作**: 直接粘贴JSON数据即可生成视频
- 📊 **实时监控**: 查看处理进度和结果
- 💻 **跨平台**: 支持所有主流操作系统

**推荐使用简易视频生成器**，只需要：
1. 复制章节的storyboards.json内容
2. 粘贴到Web界面
3. 点击生成按钮
4. 等待完成并下载视频

不再需要记忆复杂的命令行参数，享受可视化的视频剪辑处理体验！
